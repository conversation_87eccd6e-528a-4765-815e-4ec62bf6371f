#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🔍 Running pre-commit quality gates..."

# Run lint-staged for file-specific checks (linting and formatting)
echo "📝 Running lint-staged checks..."
# Add timeout to prevent hanging (5 minutes max)
if command -v gtimeout >/dev/null 2>&1; then
  # macOS with coreutils installed
  gtimeout 300 npx lint-staged
elif command -v timeout >/dev/null 2>&1; then
  # Linux/Unix systems
  timeout 300 npx lint-staged
else
  # Fallback without timeout
  npx lint-staged
fi

# Check if lint-staged failed
if [ $? -ne 0 ]; then
  echo "❌ Lint-staged failed or timed out. Please fix linting issues and try again."
  exit 1
fi

# Run comprehensive type checking
echo "🔧 Running TypeScript type checking..."
npm run type-check

# Check for console statements in production code (excluding test files and legitimate usage)
echo "🚫 Checking for console statements..."
CONSOLE_FILES=$(find src -name "*.ts" -o -name "*.tsx" | grep -v -E "(test|spec|__tests__)" | \
  grep -v "console-migration.ts" | grep -v "ErrorReporting.ts" | grep -v "loggingService.ts" | \
  xargs grep -l "console\.\(log\|info\|warn\|error\|debug\)\s*[(]" 2>/dev/null | \
  grep -v "^.*//.*console\." | grep -v "^.*\*.*console\." || true)
if [ ! -z "$CONSOLE_FILES" ]; then
  echo "❌ Console statements found in production code. Please remove them or use the logging service."
  echo "Files with problematic console statements:"
  echo "$CONSOLE_FILES"
  exit 1
fi

echo "✅ Pre-commit quality gates passed!"
