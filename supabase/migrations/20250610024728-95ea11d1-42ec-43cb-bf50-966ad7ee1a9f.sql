
-- Create custom_reports table for storing user-created reports
CREATE TABLE public.custom_reports (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  data_source TEXT NOT NULL DEFAULT 'risks',
  filters <PERSON>SON<PERSON> DEFAULT '{}',
  sections JSONB NOT NULL DEFAULT '[]',
  created_by UUID NOT NULL REFERENCES auth.users(id),
  organization_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  is_template BOOLEAN NOT NULL DEFAULT false,
  template_category TEXT
);

-- Add Row Level Security
ALTER TABLE public.custom_reports ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for custom_reports
CREATE POLICY "Users can view reports from their organization" 
  ON public.custom_reports 
  FOR SELECT 
  USING (organization_id = get_user_organization_id());

CREATE POLICY "Users can create reports for their organization" 
  ON public.custom_reports 
  FOR INSERT 
  WITH CHECK (organization_id = get_user_organization_id() AND created_by = auth.uid());

CREATE POLICY "Users can update their own reports" 
  ON public.custom_reports 
  FOR UPDATE 
  USING (organization_id = get_user_organization_id() AND created_by = auth.uid());

CREATE POLICY "Users can delete their own reports" 
  ON public.custom_reports 
  FOR DELETE 
  USING (organization_id = get_user_organization_id() AND created_by = auth.uid());

-- Create index for better performance
CREATE INDEX idx_custom_reports_organization ON public.custom_reports(organization_id);
CREATE INDEX idx_custom_reports_created_by ON public.custom_reports(created_by);
CREATE INDEX idx_custom_reports_template ON public.custom_reports(is_template);
