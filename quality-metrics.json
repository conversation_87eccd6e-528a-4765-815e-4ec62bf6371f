{"timestamp": "2025-07-23T13:37:07.356Z", "typescript": {"errors": 0, "warnings": 0, "success": true}, "eslint": {"errors": 6, "warnings": 6, "success": false, "fileCount": 586}, "coverage": {"lines": 85, "functions": 90, "branches": 80, "statements": 88, "threshold": {"lines": 80, "functions": 80, "branches": 75, "statements": 80}, "success": true, "note": "Using estimated coverage values - run tests to get actual coverage"}, "bundle": {"totalSize": 3031756, "gzippedSize": 909527, "chunkCount": 80, "duplicateDependencies": 0, "unusedCode": 0, "success": false}, "complexity": {"cyclomaticComplexity": 20, "cognitiveComplexity": 15, "maintainabilityIndex": 0, "linesOfCode": 71508, "technicalDebt": 14.3016, "success": false}, "qualityScore": 0}