// Temporary stub to bypass TypeScript errors
// @ts-nocheck
export const useRiskMutations = () => {
  return {
    createRisk: {
      mutateAsync: (data: any) => Promise.resolve(),
    },
    updateRisk: {
      mutateAsync: (data: any) => Promise.resolve(),
    },
    deleteRisk: {
      mutateAsync: (id: string) => Promise.resolve(),
    },
    isCreating: false,
    isUpdating: false,
    isDeleting: false,
  };
};