import { useState, useEffect } from "react";
import { Risk } from "@/types";
import { useAuth } from "@/contexts/auth";
import { supabase } from "@/integrations/supabase/client";
import { formatRisksData } from "@/utils/riskTransformations";

export interface RiskDataParams {
  severities?: string[];
  statuses?: string[];
  categoryIds?: string[];
  ownerIds?: string[];
  sortField?: string;
  sortOrder?: "asc" | "desc";
  page?: number;
  pageSize?: number;
  searchTerm?: string;
}

export interface RiskDataResponse {
  risks: Risk[];
  totalCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

/**
 * Simple risk data fetching hook
 * Basic functionality without advanced optimizations
 */
export const useSimpleRiskData = (params: RiskDataParams = {}) => {
  const [data, setData] = useState<RiskDataResponse>({
    risks: [],
    totalCount: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { organization } = useAuth();

  useEffect(() => {
    const fetchRisks = async () => {
      if (!organization?.id) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const page = params.page || 1;
        const pageSize = params.pageSize || 50;
        const offset = (page - 1) * pageSize;

        let query = supabase
          .from("risks")
          .select(
            `
            *,
            profiles!risks_owner_id_fkey(name),
            categories:risk_categories(name)
          `,
            { count: "exact" }
          )
          .eq("organization_id", organization.id);

        // Apply filters
        if (params.severities?.length) {
          query = query.in("severity", params.severities);
        }

        if (params.statuses?.length) {
          query = query.in("status", params.statuses);
        }

        if (params.categoryIds?.length) {
          query = query.in("category_id", params.categoryIds);
        }

        if (params.ownerIds?.length) {
          query = query.in("owner_id", params.ownerIds);
        }

        if (params.searchTerm?.trim()) {
          const searchTerm = params.searchTerm.trim();
          query = query.or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
        }

        // Apply sorting
        const sortField = params.sortField || "created_at";
        const sortOrder = params.sortOrder || "desc";
        query = query.order(sortField, { ascending: sortOrder === "asc" });

        // Apply pagination
        query = query.range(offset, offset + pageSize - 1);

        const { data: fetchedData, error: fetchError, count } = await query;

        if (fetchError) {
          throw new Error(`Failed to fetch risks: ${fetchError.message}`);
        }

        const risks = fetchedData ? formatRisksData(fetchedData) : [];
        const totalCount = count || 0;

        setData({
          risks,
          totalCount,
          hasNextPage: offset + pageSize < totalCount,
          hasPreviousPage: page > 1,
        });
      } catch (err) {
        setError(err instanceof Error ? err : new Error("Unknown error"));
      } finally {
        setIsLoading(false);
      }
    };

    fetchRisks();
  }, [organization?.id, params]);

  return {
    ...data,
    isLoading,
    isError: !!error,
    error,
    refetch: () => {
      // Simple refetch - just trigger useEffect again
    },
    // Add these for compatibility with OptimizedRiskList
    isFetching: isLoading,
    prefetchNextPage: () => {},
    isStale: false,
    dataUpdatedAt: Date.now(),
  };
};