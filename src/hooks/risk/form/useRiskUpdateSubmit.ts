import { useState } from "react";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Risk } from "@/types";
import { calculateSeverity } from "@/services/risk";
import { z } from "zod";
import { RiskFormSchema } from "@/components/risk/schema/riskFormSchema";
import { riskRepository } from "@/repositories/riskRepository";
type FormValues = z.infer<typeof RiskFormSchema>;
interface UseRiskUpdateSubmitProps {
  initialRisk: Risk;
  onSuccess?: () => void;
  categoryMapping: Record<string, string>;
}
/**
 * Get the current user's organization ID
 */
const getUserOrganizationId = async (): Promise<string | null> => {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) return null;
  const { data: profile } = await supabase
    .from("profiles")
    .select("organization_id")
    .eq("id", user.id)
    .single();
  return profile?.organization_id ?? null;
};
export const useRiskUpdateSubmit = ({
  initialRisk,
  onSuccess,
  categoryMapping,
}: UseRiskUpdateSubmitProps) => {
  const { toast } = useToast();
  const [submitting, setSubmitting] = useState(false);
  const onSubmit = async (values: FormValues) => {
    try {
      setSubmitting(true);
      // Get organization ID for security verification
      const organizationId = await getUserOrganizationId();
      if (!organizationId) {
        throw new Error("User organization not found");
      }
      // Verify the risk belongs to the user's organization
      if (initialRisk.organizationId !== organizationId) {
        throw new Error("Access denied - risk does not belong to your organization");
      }
      // Calculate both inherent and residual severities
      const inherentSeverity = calculateSeverity(values.inherentLikelihood, values.inherentImpact);
      const residualSeverity = calculateSeverity(values.likelihood, values.impact);
      // Get the category ID using the category name
      const categoryId = categoryMapping[values.category ?? ""];
      if (!categoryId && values.category) {
        throw new Error(
          `Category "${values.category}" not found. Please try again or select a different category.`
        );
      }
      // Prepare risk update data
      const updateData: Partial<Risk> = {
        title: values.title,
        description: values.description,
        categoryId: categoryId ?? "", // Use empty string instead of null
        // Inherent risk fields
        inherentLikelihood: values.inherentLikelihood,
        inherentImpact: values.inherentImpact,
        inherentSeverity: inherentSeverity,
        // Residual risk fields
        likelihood: values.likelihood,
        impact: values.impact,
        severity: residualSeverity,
        status: values.status,
        ...(values.currentControls && { currentControls: values.currentControls }),
        ...(values.mitigationApproach && { mitigationApproach: values.mitigationApproach }),
        ...(values.dueDate && { dueDate: values.dueDate }),
        ownerId: values.ownerId || initialRisk.ownerId,
        // Map form control measures to domain objects
        controlMeasures:
          values.controlMeasures?.map(control => ({
            id: control.id || "",
            riskId: initialRisk.id,
            organizationId: organizationId,
            description: control.description,
            ...(control.effectiveness && { effectiveness: control.effectiveness }),
            implemented: control.implemented ?? true,
            createdAt: new Date(), // Will be overridden by repository if existing
            updatedAt: new Date(),
          })) || [],
        // Map form mitigation actions to domain objects
        mitigationActions:
          values.mitigationActions?.map(action => ({
            id: action.id || "",
            riskId: initialRisk.id,
            organizationId: organizationId,
            description: action.description,
            completed: action.completed ?? false,
            createdAt: new Date(), // Will be overridden by repository if existing
            updatedAt: new Date(),
          })) || [],
      };
      // Update risk using repository pattern with organization verification
      const updatedRisk = await riskRepository.updateRisk(
        initialRisk.id,
        updateData,
        organizationId
      );
      if (!updatedRisk) {
        throw new Error("Failed to update risk - no data returned");
      }
      toast({
        title: "Risk Updated",
        description: "The risk has been successfully updated.",
        variant: "default",
      });
      if (onSuccess) {
        onSuccess();
      }
      return true;
    } catch (error: unknown) {
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to update risk. Please try again.",
        variant: "destructive",
      });
      return false;
    } finally {
      setSubmitting(false);
    }
  };
  return {
    submitting,
    onSubmit,
  };
};
