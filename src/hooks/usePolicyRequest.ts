import { useState } from "react";
import { PolicyRequestFormData } from "@/components/policy/form/schema";
import { useAuth } from "@/contexts/auth";
import { useToast } from "@/hooks/use-toast";
import { errorToast } from "@/components/ui/enhanced-toast";
import { createPolicyRequest, notify<PERSON><PERSON><PERSON> } from "@/services/policy";
import { uploadPolicyDocument } from "@/services/policy";

/**
 * Hook for policy request operations
 */

export const usePolicyRequest = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handlePolicyRequest = async (values: PolicyRequestFormData, referenceFile?: File) => {
    if (!user) {
      errorToast({
        title: "Authentication required",
        description: "You must be logged in to submit a policy request.",
      });
      return null;
    }

    setIsSubmitting(true);

    try {
      let referenceUrl;

      if (referenceFile) {
        // Pass the file and a generated policy request ID
        referenceUrl = await uploadPolicyDocument(referenceFile, `request-${Date.now()}`);
      }

      // Include all required properties from PolicyRequestFormData
      const policyRequest = await createPolicyRequest(
        {
          title: values.title,
          description: values.description,
          category: values.category,
          justification: values.justification,
          reference_document_url: referenceUrl, // Changed to match the snake_case DB field
        },
        user.id
      );

      // Notify admins about the new policy request
      await notifyAdmins({
        type: "new_policy_request",
        title: `New Policy Request: ${values.title}`,
        message: `${user.name ?? "A user"} has requested a new ${values.category} policy: ${values.title}`,
        metadata: {
          requestId: policyRequest.id,
          requesterId: user.id,
          requesterName: user.name || user.email,
          category: values.category,
        },
      });

      toast({
        title: "Request submitted",
        description:
          "Your policy request has been submitted successfully and administrators have been notified.",
      });

      setIsSubmitting(false);
      return policyRequest;
    } catch (error: unknown) {
      errorToast({
        title: "Failed to submit request",
        description: error.message ?? "An unexpected error occurred.",
      });
      setIsSubmitting(false);
      return null;
    }
  };

  return {
    handlePolicyRequest,
    isSubmitting,
  };
};
