import { useState, useEffect } from "react";
import { Policy } from "@/types/policy";
import { fetchPublishedPolicies, fetchPolicyById } from "@/services/policy";
import { errorToast } from "@/components/ui/enhanced-toast";

export const usePolicies = (category?: string) => {
  const [policies, setPolicies] = useState<Policy[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const loadPolicies = async () => {
      try {
        setLoading(true);
        // fetchPublishedPolicies now includes organization filtering
        const data = await fetchPublishedPolicies();

        // Apply category filtering if specified
        const filteredData =
          category && category !== "all"
            ? data.filter(policy => policy.category === category)
            : data;

        setPolicies(filteredData);
        setError(null);
      } catch (err: unknown) {
        const error = err instanceof Error ? err : new Error("An unexpected error occurred");
        setError(error);
        errorToast({
          title: "Failed to load policies",
          description: error.message,
        });
      } finally {
        setLoading(false);
      }
    };

    loadPolicies();
  }, [category]);

  return { policies, loading, error };
};

export const usePolicy = (policyId: string | undefined) => {
  const [policy, setPolicy] = useState<Policy | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const loadPolicy = async () => {
      if (!policyId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        // fetchPolicyById now includes organization verification
        const data = await fetchPolicyById(policyId);
        setPolicy(data);
        setError(null);
      } catch (err: unknown) {
        const error = err instanceof Error ? err : new Error("An unexpected error occurred");
        setError(error);
        errorToast({
          title: "Failed to load policy",
          description: error.message,
        });
      } finally {
        setLoading(false);
      }
    };

    loadPolicy();
  }, [policyId]);

  return { policy, loading, error };
};
