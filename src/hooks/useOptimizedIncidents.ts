import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/contexts/auth";
import { supabase } from "@/integrations/supabase/client";
import { Incident, RiskSeverity } from "@/types";
import { formatIncidentsData } from "@/utils/riskTransformations";
import { queryKeys } from "@/lib/query-client";
import { useMemo } from "react";

export interface IncidentDataParams {
  severity?: RiskSeverity;
  status?: "Open" | "Investigating" | "Resolved" | "Closed";
  reporterId?: string;
  relatedRiskId?: string;
  // Pagination support
  page?: number;
  pageSize?: number;
  // Search functionality
  searchTerm?: string;
}

export interface IncidentDataResponse {
  incidents: Incident[];
  totalCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

/**
 * Optimized incident data fetching with TanStack Query
 * Includes caching, background updates, and pagination
 */

export const useIncidents = (params: IncidentDataParams = {}) => {
  const { organization, user } = useAuth();
  const queryClient = useQueryClient();

  // Memoize query key to prevent unnecessary re-renders
  const queryKey = useMemo(() => {
    if (!organization?.id) return null;

    return [
      ...queryKeys.organization.all(organization.id),
      "incidents",
      {
        severity: params.severity,
        status: params.status,
        reporterId: params.reporterId,
        relatedRiskId: params.relatedRiskId,
        page: params.page ?? 1,
        pageSize: params.pageSize ?? 50,
        searchTerm: params.searchTerm?.toLowerCase().trim(),
      },
    ];
  }, [
    organization?.id,
    params.severity,
    params.status,
    params.reporterId,
    params.relatedRiskId,
    params.page,
    params.pageSize,
    params.searchTerm,
  ]);

  // Query function with optimized database queries
  const fetchIncidents = async (): Promise<IncidentDataResponse> => {
    if (!organization?.id) {
      throw new Error("Organization not available");
    }

    const page = params.page ?? 1;
    const pageSize = params.pageSize ?? 50;
    const offset = (page - 1) * pageSize;

    // Build optimized query with pagination
    let query = supabase
      .from("incidents")
      .select(
        `
        *,
        profiles!incidents_reporter_id_fkey(name),
        risks!incidents_related_risk_id_fkey(title)
      `,
        { count: "exact" }
      )
      .eq("organization_id", organization.id);

    // Apply filters efficiently
    if (params.severity) {
      query = query.eq("severity", params.severity);
    }

    if (params.status) {
      query = query.eq("status", params.status);
    }

    if (params.reporterId) {
      query = query.eq("reporter_id", params.reporterId);
    }

    if (params.relatedRiskId) {
      query = query.eq("related_risk_id", params.relatedRiskId);
    }

    // Search functionality
    if (params.searchTerm?.trim()) {
      const searchTerm = params.searchTerm.trim();
      query = query.or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
    }

    // Apply sorting (most recent first)
    query = query.order("created_at", { ascending: false });

    // Apply pagination
    query = query.range(offset, offset + pageSize - 1);

    const { data, error, count } = await query;

    if (error) {
      throw new Error(`Failed to fetch incidents: ${error.message}`);
    }

    const incidents = data ? formatIncidentsData(data) : [];
    const totalCount = count ?? 0;

    return {
      incidents,
      totalCount,
      hasNextPage: offset + pageSize < totalCount,
      hasPreviousPage: page > 1,
    };
  };

  // Use TanStack Query with optimized configuration
  const query = useQuery({
    queryKey: queryKey || [],
    queryFn: fetchIncidents,
    enabled: !!organization?.id && !!user?.id && !!queryKey,

    // Cache configuration for incidents
    staleTime: 2 * 60 * 1000, // 2 minutes - incidents are time-sensitive
    gcTime: 8 * 60 * 1000, // 8 minutes in cache

    // Background refetch for critical data
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,

    // Error retry configuration
    retry: (failureCount, error: Error | unknown) => {
      // Don't retry on authentication errors
      const errorMessage = error instanceof Error ? error.message : '';
      const errorStatus = error && typeof error === 'object' && 'status' in error ? (error as any).status : null;
      if (errorMessage.includes("auth") || errorStatus === 401) {
        return false;
      }
      return failureCount < 2;
    },

    // Structural sharing for performance
    structuralSharing: true,
  });

  // Prefetch next page for better UX
  const prefetchNextPage = () => {
    if (!query.data?.hasNextPage || !queryKey) return;

    const nextPageParams = { ...params, page: (params.page ?? 1) + 1 };
    const nextPageKey = [
      ...queryKeys.organization.all(organization!.id),
      "incidents",
      nextPageParams,
    ];

    queryClient.prefetchQuery({
      queryKey: nextPageKey,
      queryFn: () => fetchIncidents(),
      staleTime: 1 * 60 * 1000, // 1 minute for prefetched data
    });
  };

  return {
    // Data
    incidents: query.data?.incidents ?? [],
    totalCount: query.data?.totalCount ?? 0,
    hasNextPage: query.data?.hasNextPage ?? false,
    hasPreviousPage: query.data?.hasPreviousPage ?? false,

    // Loading states
    isLoading: query.isLoading,
    isFetching: query.isFetching,
    isError: query.isError,
    error: query.error,

    // Actions
    refetch: query.refetch,
    prefetchNextPage,

    // Query status
    isStale: query.isStale,
    dataUpdatedAt: query.dataUpdatedAt,

    // Legacy compatibility
    loading: query.isLoading,
  };
};
