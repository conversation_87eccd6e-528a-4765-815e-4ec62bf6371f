import * as React from "react";
import { useIsMobile } from "@/hooks/use-mobile";

export interface PerformanceMetrics {
  // Core Web Vitals
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  // Other metrics
  fcp?: number; // First Contentful Paint
  ttfb?: number; // Time to First Byte
  // Custom metrics
  routeChangeTime?: number;
  componentRenderTime?: number;
  // Device info
  deviceMemory?: number;
  connectionType?: string;
  isLowEndDevice?: boolean;
}

export interface PerformanceThresholds {
  lcp: { good: number; poor: number };
  fid: { good: number; poor: number };
  cls: { good: number; poor: number };
  fcp: { good: number; poor: number };
  ttfb: { good: number; poor: number };
}

const DEFAULT_THRESHOLDS: PerformanceThresholds = {
  lcp: { good: 2500, poor: 4000 },
  fid: { good: 100, poor: 300 },
  cls: { good: 0.1, poor: 0.25 },
  fcp: { good: 1800, poor: 3000 },
  ttfb: { good: 800, poor: 1800 },
};

// Helper function to get device info
function getDeviceInfo() {
  const deviceMemory = (navigator as any).deviceMemory || 4;
  const connection = (navigator as any).connection || {};
  return {
    deviceMemory,
    connectionType: connection.effectiveType || "unknown",
    isLowEndDevice:
      deviceMemory < 4 ||
      connection.effectiveType === "slow-2g" ||
      connection.effectiveType === "2g",
  };
}

// Helper function to measure Core Web Vitals
function measureCoreWebVitals(_callback: (metric: string, value: number) => void) {
  // Implementation would use web-vitals library or native APIs
  // For now, return empty to avoid build errors
  return;
}

// Helper function to measure navigation timing
async function measureNavigationTiming() {
  const navigation = performance.getEntriesByType("navigation")[0] as PerformanceNavigationTiming;
  if (!navigation) return {};

  return {
    fcp: navigation.responseEnd - navigation.fetchStart,
    ttfb: navigation.responseStart - navigation.requestStart,
  };
}

/**
 * Hook for monitoring Core Web Vitals and performance metrics
 */
export function usePerformanceMetrics() {
  const [metrics, setMetrics] = React.useState<PerformanceMetrics>({});
  const isMobile = useIsMobile();

  const getPerformanceScore = React.useCallback(
    (metric: keyof PerformanceMetrics, value: number) => {
      const threshold = DEFAULT_THRESHOLDS[metric as keyof PerformanceThresholds];
      if (!threshold) return "unknown";

      if (value <= threshold.good) return "good";
      if (value <= threshold.poor) return "needs-improvement";
      return "poor";
    },
    []
  );

  const getOverallScore = React.useCallback(() => {
    const scores = ["lcp", "fid", "cls", "fcp"]
      .map(metric => {
        const value = metrics[metric as keyof PerformanceMetrics];
        return typeof value === "number"
          ? getPerformanceScore(metric as keyof PerformanceMetrics, value)
          : null;
      })
      .filter(Boolean);

    if (scores.length === 0) return null;
    const goodCount = scores.filter(score => score === "good").length;
    const poorCount = scores.filter(score => score === "poor").length;

    if (goodCount >= scores.length * 0.75) return "good";
    if (poorCount >= scores.length * 0.5) return "poor";
    return "needs-improvement";
  }, [metrics, getPerformanceScore]);

  React.useEffect(() => {
    // Get device capabilities
    const deviceInfo = getDeviceInfo();
    setMetrics(prev => ({ ...prev, ...deviceInfo }));

    // Measure Core Web Vitals
    measureCoreWebVitals((metric, value) => {
      setMetrics(prev => ({ ...prev, [metric]: value }));
    });

    // Measure other performance metrics
    measureNavigationTiming()
      .then(navMetrics => {
        setMetrics(prev => ({ ...prev, ...navMetrics }));
      })
      .catch(() => {
        // Silently handle errors in performance measurement
      });
  }, []);

  return {
    metrics,
    getPerformanceScore,
    overallScore: getOverallScore(),
    isLowEndDevice: metrics.isLowEndDevice,
    isMobile,
  };
}

/**
 * Hook for measuring component render performance
 */
export function useRenderPerformance(_componentName: string) {
  const renderStartTime = React.useRef<number>(0);
  const [renderTime, setRenderTime] = React.useState<number>(0);

  React.useEffect(() => {
    renderStartTime.current = performance.now();
  });

  React.useLayoutEffect(() => {
    const endTime = performance.now();
    const duration = endTime - renderStartTime.current;
    setRenderTime(duration);

    // Log slow renders only in development
    if (import.meta.env.MODE === "development" && duration > 16) {
      // Performance warning for slow render
    }
  }, []);

  return {
    renderTime,
    isSlowRender: renderTime > 16,
  };
}

/**
 * Hook for route change performance monitoring
 */
export function useRoutePerformance() {
  const [routeChangeTime, setRouteChangeTime] = React.useState<number>(0);
  const routeStartTime = React.useRef<number>(0);

  const startRouteChange = React.useCallback(() => {
    routeStartTime.current = performance.now();
  }, []);

  const endRouteChange = React.useCallback(() => {
    if (routeStartTime.current > 0) {
      const duration = performance.now() - routeStartTime.current;
      setRouteChangeTime(duration);
      routeStartTime.current = 0;
    }
  }, []);

  return {
    routeChangeTime,
    startRouteChange,
    endRouteChange,
    isSlowRouteChange: routeChangeTime > 1000,
  };
}
