import { useState } from "react";
import { useToast } from "@/components/ui/use-toast";
import { MitigationAction } from "@/types";
import { mitigationActionsRepository } from "@/repositories/mitigationActionsRepository";
import { useAuth } from "@/contexts/auth";
/**
 * Custom hook for managing mitigation actions
 * Uses repository pattern for data access and consistent error handling
 */
export const useMitigationActions = (riskId: string, onActionsUpdated?: () => void) => {
  const { toast } = useToast();
  const { organization } = useAuth();
  const [loading, setLoading] = useState(false);
  const toggleActionCompletion = async (actionId: string, completed: boolean) => {
    try {
      setLoading(true);
      if (!organization?.id) {
        throw new Error("No organization context found");
      }
      const success = await mitigationActionsRepository.updateMitigationAction(
        actionId,
        { completed },
        organization.id
      );
      if (!success) {
        throw new Error("Failed to update mitigation action");
      }
      toast({
        title: completed ? "Action marked as completed" : "Action marked as incomplete",
        description: "The mitigation action has been updated successfully.",
      });
      if (onActionsUpdated) {
        onActionsUpdated();
      }
      return true;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update the mitigation action";
      toast({
        title: "Error updating action",
        description: errorMessage,
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  };
  const addMitigationAction = async (description: string) => {
    try {
      setLoading(true);
      if (!organization?.id) {
        throw new Error("No organization context found");
      }
      const newAction = await mitigationActionsRepository.createMitigationAction({
        riskId,
        description,
        completed: false,
        organizationId: organization.id,
      });
      if (!newAction) {
        throw new Error("Failed to add mitigation action");
      }
      toast({
        title: "Action added",
        description: "A new mitigation action has been added.",
      });
      if (onActionsUpdated) {
        onActionsUpdated();
      }
      return newAction;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to add the mitigation action";
      toast({
        title: "Error adding action",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  };
  const deleteMitigationAction = async (actionId: string) => {
    try {
      setLoading(true);
      if (!organization?.id) {
        throw new Error("No organization context found");
      }
      const success = await mitigationActionsRepository.deleteMitigationAction(
        actionId,
        organization.id
      );
      if (!success) {
        throw new Error("Failed to delete mitigation action");
      }
      toast({
        title: "Action deleted",
        description: "The mitigation action has been removed.",
      });
      if (onActionsUpdated) {
        onActionsUpdated();
      }
      return true;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete the mitigation action";
      toast({
        title: "Error deleting action",
        description: errorMessage,
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  };
  const updateMitigationAction = async (actionId: string, data: Partial<MitigationAction>) => {
    try {
      setLoading(true);
      if (!organization?.id) {
        throw new Error("No organization context found");
      }
      const success = await mitigationActionsRepository.updateMitigationAction(
        actionId,
        data,
        organization.id
      );
      if (!success) {
        throw new Error("Failed to update mitigation action");
      }
      toast({
        title: "Action updated",
        description: "The mitigation action has been updated successfully.",
      });
      if (onActionsUpdated) {
        onActionsUpdated();
      }
      return true;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update the mitigation action";
      toast({
        title: "Error updating action",
        description: errorMessage,
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  };
  return {
    loading,
    toggleActionCompletion,
    addMitigationAction,
    deleteMitigationAction,
    updateMitigationAction,
  };
};
