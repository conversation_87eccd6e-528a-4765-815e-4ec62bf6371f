import { useQueries } from "@tanstack/react-query";
import { useAuth } from "@/contexts/auth";
import { queryKeys } from "@/lib/query-client";
import { RiskService } from "@/services/risk-service";
import { supabase } from "@/integrations/supabase/client";
import { formatIncidentsData } from "@/utils/riskTransformations";
import { useMemo } from "react";

/**
 * Optimized dashboard data hook using parallel queries and caching
 * Fetches multiple data sources efficiently for dashboard display
 */

export const useDashboardData = () => {
  const { organization, user } = useAuth();

  // Parallel queries for better performance
  const queries = useQueries({
    queries: [
      // Risk statistics query
      {
        queryKey: queryKeys.organization.dashboard(organization?.id ?? "").concat(["risk-stats"]),
        queryFn: () => RiskService.getRiskStatistics(organization!.id),
        enabled: !!organization?.id && !!user?.id,
        staleTime: 5 * 60 * 1000, // 5 minutes - dashboard stats don't change frequently
        gcTime: 15 * 60 * 1000, // Keep in cache for 15 minutes
      },

      // All risks query (for dashboard charts)
      {
        queryKey: queryKeys.organization.dashboard(organization?.id ?? "").concat(["all-risks"]),
        queryFn: async () => {
          if (!organization?.id) throw new Error("Organization not available");

          const { data, error } = await supabase
            .from("risks")
            .select("*")
            .eq("organization_id", organization.id)
            .order("created_at", { ascending: false });

          if (error) {
            throw new Error(`Failed to fetch risks: ${error.message}`);
          }

          return data ?? [];
        },
        enabled: !!organization?.id && !!user?.id,
        staleTime: 3 * 60 * 1000, // 3 minutes
        gcTime: 10 * 60 * 1000,
      },

      // Critical risks query
      {
        queryKey: queryKeys.organization
          .dashboard(organization?.id ?? "")
          .concat(["critical-risks"]),
        queryFn: () => RiskService.getCriticalRisks(organization!.id),
        enabled: !!organization?.id && !!user?.id,
        staleTime: 3 * 60 * 1000, // 3 minutes - critical risks need fresher data
        gcTime: 10 * 60 * 1000,
      },

      // Recent incidents query
      {
        queryKey: queryKeys.organization
          .dashboard(organization?.id ?? "")
          .concat(["recent-incidents"]),
        queryFn: async () => {
          if (!organization?.id) throw new Error("Organization not available");

          const { data, error } = await supabase
            .from("incidents")
            .select(
              `
              *,
              profiles!incidents_reporter_id_fkey(name),
              risks!incidents_related_risk_id_fkey(title)
            `
            )
            .eq("organization_id", organization.id)
            .order("date", { ascending: false })
            .limit(10);

          if (error) {
            throw new Error(`Failed to fetch recent incidents: ${error.message}`);
          }

          return data ? formatIncidentsData(data) : [];
        },
        enabled: !!organization?.id && !!user?.id,
        staleTime: 2 * 60 * 1000, // 2 minutes - incidents are time-sensitive
        gcTime: 8 * 60 * 1000,
      },

      // Risk trends query (last 30 days)
      {
        queryKey: queryKeys.organization.dashboard(organization?.id ?? "").concat(["risk-trends"]),
        queryFn: async () => {
          if (!organization?.id) throw new Error("Organization not available");

          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

          const { data, error } = await supabase
            .from("risks")
            .select("created_at, severity, status")
            .eq("organization_id", organization.id)
            .gte("created_at", thirtyDaysAgo.toISOString())
            .order("created_at", { ascending: true });

          if (error) {
            throw new Error(`Failed to fetch risk trends: ${error.message}`);
          }

          // Process trend data
          const trendData =
            data?.reduce(
              (acc, risk) => {
                const date = new Date(risk.created_at).toISOString().split("T")[0];
                if (!acc[date]) {
                  acc[date] = { total: 0, high: 0, critical: 0 };
                }
                acc[date].total += 1;
                if (risk.severity === "high") acc[date]['high'] += 1;
                if (risk.severity === "critical") acc[date]['critical'] += 1;
                return acc;
              },
              {} as Record<string, { total: number; high: number; critical: number }>
            ) || {};

          return Object.entries(trendData).map(([date, counts]) => ({
            date,
            ...counts,
          }));
        },
        enabled: !!organization?.id && !!user?.id,
        staleTime: 10 * 60 * 1000, // 10 minutes - trends don't change frequently
        gcTime: 20 * 60 * 1000,
      },
    ],
  });

  // Extract individual query results
  const [riskStatsQuery, allRisksQuery, criticalRisksQuery, recentIncidentsQuery, riskTrendsQuery] =
    queries;

  // Memoized computed values
  const dashboardMetrics = useMemo(() => {
    const riskStats = riskStatsQuery.data;
    const criticalRisks = criticalRisksQuery.data ?? [];
    const recentIncidents = recentIncidentsQuery.data ?? [];
    const riskTrends = riskTrendsQuery.data ?? [];

    if (!riskStats) return null;

    return {
      // Risk overview
      totalRisks: riskStats.totalRisks,
      risksByCategory: riskStats.categoryDistribution,
      risksBySeverity: riskStats.severityDistribution,
      risksByStatus: riskStats.statusDistribution,

      // Critical items
      criticalRisks,
      recentIncidents,

      // Trends
      riskTrends,

      // Computed metrics
      highRiskCount:
        (riskStats.severityDistribution['high'] ?? 0) + (riskStats.severityDistribution['critical'] ?? 0),
      openRiskCount:
        (riskStats.statusDistribution['open'] ?? 0) + (riskStats.statusDistribution['in_progress'] ?? 0),
      upcomingMitigations: criticalRisks.filter(
        risk =>
          risk.dueDate &&
          risk.dueDate > new Date() &&
          risk.dueDate <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // Next 7 days
      ),
    };
  }, [
    riskStatsQuery.data,
    criticalRisksQuery.data,
    recentIncidentsQuery.data,
    riskTrendsQuery.data,
  ]);

  // Combined loading and error states
  const isLoading = queries.some(query => query.isLoading);
  const isFetching = queries.some(query => query.isFetching);
  const isError = queries.some(query => query.isError);
  const errors = queries.filter(query => query.error).map(query => query.error);

  // Refetch all dashboard data
  const refetchAll = () => {
    queries.forEach(query => query.refetch());
  };

  return {
    // Data
    dashboardMetrics,

    // Individual query data (for specific components that need it)
    riskStats: riskStatsQuery.data,
    allRisks: allRisksQuery.data ?? [],
    criticalRisks: criticalRisksQuery.data ?? [],
    recentIncidents: recentIncidentsQuery.data ?? [],
    riskTrends: riskTrendsQuery.data ?? [],

    // Loading states
    isLoading,
    isFetching,
    isError,
    errors,

    // Actions
    refetchAll,

    // Individual query states (for granular loading indicators)
    queryStates: {
      riskStats: {
        isLoading: riskStatsQuery.isLoading,
        isFetching: riskStatsQuery.isFetching,
        isError: riskStatsQuery.isError,
        error: riskStatsQuery.error,
      },
      allRisks: {
        isLoading: allRisksQuery.isLoading,
        isFetching: allRisksQuery.isFetching,
        isError: allRisksQuery.isError,
        error: allRisksQuery.error,
      },
      criticalRisks: {
        isLoading: criticalRisksQuery.isLoading,
        isFetching: criticalRisksQuery.isFetching,
        isError: criticalRisksQuery.isError,
        error: criticalRisksQuery.error,
      },
      recentIncidents: {
        isLoading: recentIncidentsQuery.isLoading,
        isFetching: recentIncidentsQuery.isFetching,
        isError: recentIncidentsQuery.isError,
        error: recentIncidentsQuery.error,
      },
      riskTrends: {
        isLoading: riskTrendsQuery.isLoading,
        isFetching: riskTrendsQuery.isFetching,
        isError: riskTrendsQuery.isError,
        error: riskTrendsQuery.error,
      },
    },
  };
};
