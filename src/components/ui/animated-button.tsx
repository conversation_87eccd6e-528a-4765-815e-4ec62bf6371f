
import { Button, ButtonProps } from "@/components/ui/button";
import { motion } from "framer-motion";
import { forwardRef, ReactNode } from "react";
import { cn } from "@/lib/utils";

interface AnimatedButtonProps extends ButtonProps {
  animation?: "bounce" | "pulse" | "wiggle" | "fade";
  children: ReactNode;
}

export const AnimatedButton = forwardRef<HTMLButtonElement, AnimatedButtonProps>(
  ({ animation = "bounce", className, children, ...props }, ref) => {
    const animations = {
      bounce: {
        tap: { scale: 0.95 },
        hover: { y: -2 },
      },
      pulse: {
        tap: { scale: 0.95 },
        hover: { scale: 1.05 },
      },
      wiggle: {
        tap: { rotate: [0, -5, 5, -5, 0], transition: { duration: 0.5 } },
        hover: { scale: 1.05 },
      },
      fade: {
        tap: { opacity: 0.8 },
        hover: { opacity: 0.8 },
      },
    };

    return (
      <motion.div 
        whileTap={animations[animation].tap}
        whileHover={animations[animation].hover}
        transition={{ type: "spring", stiffness: 400, damping: 10 }}
      >
        <Button
          ref={ref}
          className={cn(className)}
          {...props}
        >
          {children}
        </Button>
      </motion.div>
    );
  }
);

AnimatedButton.displayName = "AnimatedButton";
