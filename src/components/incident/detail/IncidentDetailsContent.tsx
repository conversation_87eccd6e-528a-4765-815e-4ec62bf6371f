
import { format } from "date-fns";
import { <PERSON> } from "react-router-dom";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Incident } from "@/types";
import StatusIndicator from "@/components/incident/StatusIndicator";

interface IncidentDetailsContentProps {
  incident: Incident;
}

const IncidentDetailsContent = ({ incident }: IncidentDetailsContentProps) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle>Description</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="whitespace-pre-wrap">{incident.description}</p>
        </CardContent>
      </Card>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-2">
              <span className="text-sm font-medium text-muted-foreground">Reported By</span>
              <span className="font-medium">{incident.reporterName}</span>
            </div>
            
            <div className="grid grid-cols-1 gap-2">
              <span className="text-sm font-medium text-muted-foreground">Date Reported</span>
              <span className="text-sm">{format(incident.date, 'MMM d, yyyy, h:mm a')}</span>
            </div>

            <div className="grid grid-cols-1 gap-2">
              <span className="text-sm font-medium text-muted-foreground">Status</span>
              <StatusIndicator status={incident.status} />
            </div>
            
            {incident.relatedRiskId && (
              <div className="grid grid-cols-1 gap-2">
                <span className="text-sm font-medium text-muted-foreground">Related Risk</span>
                <Link 
                  to={`/risks/${incident.relatedRiskId}`}
                  className="text-blue-600 hover:underline text-sm font-medium"
                >
                  {incident.relatedRiskTitle ?? 'View Risk'}
                </Link>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default IncidentDetailsContent;
