import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClientProvider } from "@tanstack/react-query";
import { Routes, Route } from "react-router-dom";
import { ThemeProvider } from "next-themes";
import { AuthProvider } from "@/contexts/auth";
import { LoadingProvider } from "@/contexts/loading-context";
import ProtectedRoute from "@/components/route/ProtectedRoute";
import { PageErrorBoundary } from "@/components/error-boundaries";
import { initializeErrorHandling } from "@/utils/errors/examples/IntegrationExamples";
import { useRoutePreloader } from "@/hooks/useRoutePreloader";
import { verifyCodeSplitting, analyzeBundleSizes } from "@/utils/bundle-analyzer";
import { createQueryClient } from "@/lib/query-client";
import { initializePerformanceMonitoring } from "@/utils/performance-observer-manager";
// Lazy-loaded route components
import {
  LazyIndex,
  <PERSON>zyLogin,
  LazySignup,
  LazyDashboard,
  LazyRiskRegister,
  LazyRiskCreate,
  LazyRiskDetails,
  LazyRiskTemplates,
  LazyIncidents,
  LazyIncidentCreate,
  LazyIncidentEdit,
  LazyIncidentDetails,
  LazyReports,
  LazyAdministration,
  LazyOrganizationManagement,
  LazyOrganizationPage,
  LazyPolicies,
  LazyPolicyEditDialog,
  LazyProfile,
  LazyNotFound,
} from "@/routes/lazy-routes";
import "./App.css";
// Initialize error handling system safely
try {
  initializeErrorHandling();
} catch (error) {
  // Error caught and handled
}
// Development tools - only run in development mode
if (import.meta.env.MODE === "development") {
  setTimeout(() => {
    try {
      verifyCodeSplitting();
      analyzeBundleSizes();
      initializePerformanceMonitoring();
    } catch (error) {
      // Only log errors in development
    }
  }, 2000);
}
const queryClient = createQueryClient();
function App() {
  try {
    // Enable intelligent route preloading
    useRoutePreloader();
    return (
      <PageErrorBoundary pageName="app">
        <QueryClientProvider client={queryClient}>
          <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
            <TooltipProvider>
              <LoadingProvider>
                <AuthProvider>
                  <Toaster />
                  <Routes>
                    {/* Public routes */}
                    <Route path="/" element={<LazyIndex />} />
                    <Route path="/login" element={<LazyLogin />} />
                    <Route path="/signup" element={<LazySignup />} />
                    {/* Protected routes */}
                    <Route element={<ProtectedRoute />}>
                      <Route path="/dashboard" element={<LazyDashboard />} />
                      {/* Risk routes */}
                      <Route path="/risks" element={<LazyRiskRegister />} />
                      <Route path="/risks/create" element={<LazyRiskCreate />} />
                      <Route path="/risks/templates" element={<LazyRiskTemplates />} />
                      <Route path="/risks/:id" element={<LazyRiskDetails />} />
                      {/* Incident routes */}
                      <Route path="/incidents" element={<LazyIncidents />} />
                      <Route path="/incidents/new" element={<LazyIncidentCreate />} />
                      <Route path="/incidents/:id/edit" element={<LazyIncidentEdit />} />
                      <Route path="/incidents/:id" element={<LazyIncidentDetails />} />
                      {/* Policy routes */}
                      <Route path="/policies" element={<LazyPolicies />} />
                      <Route path="/policies/:policyId" element={<LazyPolicies />} />
                      <Route path="/policy-admin/edit/:policyId" element={<LazyPolicyEditDialog />} />
                      {/* Administration routes */}
                      <Route path="/administration" element={<LazyAdministration />} />
                      <Route path="/policy-admin" element={<LazyAdministration />} />
                      <Route path="/user-management" element={<LazyAdministration />} />
                      <Route path="/users" element={<LazyAdministration />} />
                      <Route path="/risk-categories" element={<LazyAdministration />} />
                      {/* Other protected routes */}
                      <Route path="/reports" element={<LazyReports />} />
                      <Route path="/organization" element={<LazyOrganizationPage />} />
                      <Route
                        path="/organization/management"
                        element={<LazyOrganizationManagement />}
                      />
                      <Route path="/profile" element={<LazyProfile />} />
                    </Route>
                    {/* Catch all */}
                    <Route path="*" element={<LazyNotFound />} />
                  </Routes>
                </AuthProvider>
              </LoadingProvider>
            </TooltipProvider>
          </ThemeProvider>
        </QueryClientProvider>
      </PageErrorBoundary>
    );
  } catch (error) {
    return (
      <div
        style={{
          padding: "20px",
          textAlign: "center",
          fontFamily: "Arial, sans-serif",
          color: "#dc2626",
        }}
      >
        <h1>Application Error</h1>
        <p>Something went wrong loading the application.</p>
        <p>Please refresh the page or contact support.</p>
        <details style={{ marginTop: "20px", textAlign: "left" }}>
          <summary>Error Details</summary>
          <pre style={{ background: "#f5f5f5", padding: "10px", marginTop: "10px" }}>
            {error instanceof Error ? error.message : String(error)}
          </pre>
        </details>
      </div>
    );
  }
}
export default App;
