#!/bin/bash

# Safe commit script to prevent hanging during git commit
# This script provides a timeout mechanism for git commits

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default timeout (5 minutes)
TIMEOUT=${1:-300}

echo -e "${YELLOW}🚀 Starting safe commit with ${TIMEOUT}s timeout...${NC}"

# Function to run git commit with timeout
safe_git_commit() {
    local commit_message="$1"
    
    if command -v gtimeout >/dev/null 2>&1; then
        # macOS with coreutils installed
        echo -e "${YELLOW}📝 Running git commit with gtimeout...${NC}"
        gtimeout $TIMEOUT git commit -m "$commit_message"
    elif command -v timeout >/dev/null 2>&1; then
        # Linux/Unix systems
        echo -e "${YELLOW}📝 Running git commit with timeout...${NC}"
        timeout $TIMEOUT git commit -m "$commit_message"
    else
        # Fallback without timeout
        echo -e "${YELLOW}⚠️  No timeout command available, running git commit normally...${NC}"
        git commit -m "$commit_message"
    fi
}

# Check if we have staged changes
if ! git diff --cached --quiet; then
    echo -e "${GREEN}✅ Found staged changes, proceeding with commit...${NC}"
    
    # Get commit message
    if [ -z "$2" ]; then
        echo -e "${YELLOW}💬 Please enter your commit message:${NC}"
        read -r commit_message
    else
        commit_message="$2"
    fi
    
    # Run the safe commit
    if safe_git_commit "$commit_message"; then
        echo -e "${GREEN}✅ Commit successful!${NC}"
    else
        exit_code=$?
        if [ $exit_code -eq 124 ] || [ $exit_code -eq 143 ]; then
            echo -e "${RED}❌ Commit timed out after ${TIMEOUT} seconds!${NC}"
            echo -e "${YELLOW}💡 Try running: npm run lint:fix && npm run format${NC}"
            echo -e "${YELLOW}💡 Then commit again with a shorter timeout or fix linting issues${NC}"
        else
            echo -e "${RED}❌ Commit failed with exit code $exit_code${NC}"
        fi
        exit $exit_code
    fi
else
    echo -e "${YELLOW}⚠️  No staged changes found. Please stage your changes first with 'git add'${NC}"
    exit 1
fi
